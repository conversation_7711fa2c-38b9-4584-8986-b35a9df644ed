'use client';

import { useEffect, useState } from 'react';
import gradientGL from 'gradient-gl';

/**
 * Page-level gradient manager that creates multiple gradient canvases
 * This component manages all gradients on the page to prevent conflicts
 */
const PageGradientManager = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined' && !isInitialized) {
      const initializeGradients = async () => {
        try {
          // Create gradient containers for each section
          const featuresContainer = document.getElementById('features-gradient-bg');
          const ctaContainer = document.getElementById('cta-gradient-bg');

          if (featuresContainer && ctaContainer) {
            console.log('Initializing page gradients...');
            
            // Try a different approach: use the same shader for both
            try {
              // Initialize features gradient first
              await gradientGL('b5.fc04', '#features-gradient-bg');
              console.log('Features gradient initialized');

              // For CTA, use CSS fallback since gradient-gl only supports one instance
              ctaContainer.classList.add('gradient-fallback');
              console.log('CTA using CSS fallback gradient');
              
            } catch (error) {
              console.warn('Features gradient failed, using fallback:', error);
              featuresContainer.classList.add('gradient-fallback');
            }
          }
          
          setIsInitialized(true);
        } catch (error) {
          console.error('Failed to initialize page gradients:', error);
        }
      };

      // Initialize after a short delay to ensure DOM is ready
      setTimeout(initializeGradients, 500);
    }
  }, [isInitialized]);

  return null; // This component doesn't render anything
};

export default PageGradientManager;
