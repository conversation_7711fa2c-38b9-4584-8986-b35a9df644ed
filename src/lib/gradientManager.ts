/**
 * Multi-instance gradient manager
 * This creates a solution that actually supports multiple gradient instances
 */

// Store our instances
const activeInstances = new Map<string, any>();

/**
 * Simple approach: Use the original gradient-gl but with better coordination
 * The key insight is that we need to ensure only one gradient is "active" at a time
 * but both can exist and be rendered
 */
export async function initializeGradient(
  seed: string,
  selector: string,
  instanceId: string
): Promise<any> {
  try {
    console.log(`Attempting to initialize gradient ${instanceId} with seed ${seed}`);

    // Clean up any existing instance with this ID
    if (activeInstances.has(instanceId)) {
      const existingInstance = activeInstances.get(instanceId);
      if (existingInstance && typeof existingInstance.destroy === 'function') {
        existingInstance.destroy();
      }
      activeInstances.delete(instanceId);
    }

    // Import gradient-gl fresh each time
    const { default: gradientGL } = await import('gradient-gl');

    // Create the gradient instance
    const instance = await gradientGL(seed, selector);

    // Store the instance
    activeInstances.set(instanceId, instance);

    console.log(`Successfully initialized gradient ${instanceId}`);

    return instance;
  } catch (error) {
    console.warn(`Failed to initialize gradient ${instanceId}:`, error);

    // Fallback: add CSS gradient class to the element
    const element = document.querySelector(selector);
    if (element) {
      element.classList.add('gradient-fallback');
      console.log(`Applied CSS fallback for ${instanceId}`);
    }

    return null;
  }
}

/**
 * Destroy a specific gradient instance
 */
export function destroyGradient(instanceId: string): void {
  const instance = activeInstances.get(instanceId);
  if (instance && typeof instance.destroy === 'function') {
    instance.destroy();
  }
  activeInstances.delete(instanceId);
}

/**
 * Destroy all gradient instances
 */
export function destroyAllGradients(): void {
  for (const [instanceId] of activeInstances) {
    destroyGradient(instanceId);
  }
}

/**
 * Get a specific gradient instance
 */
export function getGradientInstance(instanceId: string): any {
  return activeInstances.get(instanceId);
}

/**
 * Check if a gradient instance exists and is active
 */
export function hasGradientInstance(instanceId: string): boolean {
  return activeInstances.has(instanceId);
}

/**
 * Initialize multiple gradients sequentially to avoid conflicts
 * This is the recommended way to initialize multiple gradients
 */
export async function initializeMultipleGradients(
  gradients: Array<{ seed: string; selector: string; instanceId: string }>
): Promise<void> {
  for (const gradient of gradients) {
    await initializeGradient(gradient.seed, gradient.selector, gradient.instanceId);
  }
}
